# 清理JavaScript文件中的调试代码和死代码
# 这个脚本会识别和清理常见的调试代码模式

Write-Host "开始清理调试代码和死代码..." -ForegroundColor Green

# 定义要清理的模式
$debugPatterns = @(
    "// 🚨 强制DEBUG：.*",
    "console\.log\('🔧.*",
    "console\.log\('🔍.*",
    "console\.log\('📋.*",
    "console\.log\('📊.*",
    "console\.log\('⚡.*",
    "console\.log\('🎯.*",
    "console\.log\('✅.*",
    "console\.log\('❌.*",
    "console\.error\('❌.*",
    "console\.warn\('⚠️.*",
    "console\.debug\(.*",
    "// TODO:.*",
    "// FIXME:.*",
    "// HACK:.*",
    "// DEBUG:.*",
    "^\s*//\s*console\..*",
    "^\s*//.*测试.*",
    "^\s*//.*调试.*"
)

# 获取所有JS文件
$jsFiles = Get-ChildItem -Path "js" -Recurse -Filter "*.js"

$totalCleaned = 0
$filesModified = 0

foreach ($file in $jsFiles) {
    Write-Host "检查文件: $($file.FullName)" -ForegroundColor Yellow
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    $linesCleaned = 0
    
    # 应用每个清理模式
    foreach ($pattern in $debugPatterns) {
        $matches = [regex]::Matches($content, $pattern, [System.Text.RegularExpressions.RegexOptions]::Multiline)
        if ($matches.Count -gt 0) {
            Write-Host "  发现 $($matches.Count) 个匹配项: $pattern" -ForegroundColor Cyan
            $content = [regex]::Replace($content, $pattern, "", [System.Text.RegularExpressions.RegexOptions]::Multiline)
            $linesCleaned += $matches.Count
        }
    }
    
    # 清理空行（连续的空行减少为单个空行）
    $content = [regex]::Replace($content, "(\r?\n\s*){3,}", "`r`n`r`n", [System.Text.RegularExpressions.RegexOptions]::Multiline)
    
    # 如果内容有变化，保存文件
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  ✅ 已清理 $linesCleaned 行调试代码" -ForegroundColor Green
        $totalCleaned += $linesCleaned
        $filesModified++
    } else {
        Write-Host "  ✓ 无需清理" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "清理完成!" -ForegroundColor Green
Write-Host "修改的文件数: $filesModified" -ForegroundColor White
Write-Host "清理的代码行数: $totalCleaned" -ForegroundColor White
