# Analyze unloaded JavaScript files
# Compare all JS files in project with index.html references

Write-Host "Analyzing JavaScript file loading..." -ForegroundColor Green

# Get all JS files
$allJsFiles = Get-ChildItem -Path "." -Recurse -Filter "*.js" | ForEach-Object {
    $relativePath = $_.FullName.Replace((Get-Location).Path + '\', '').Replace('\', '/')
    [PSCustomObject]@{
        Path = $relativePath
        Name = $_.Name
        Size = $_.Length
        LastModified = $_.LastWriteTime
        Directory = Split-Path $relativePath -Parent
    }
} | Sort-Object Path

# Extract referenced files from index.html
$loadedFiles = Select-String -Path "index.html" -Pattern 'src="([^"]*\.js)"' | ForEach-Object {
    $_.Matches[0].Groups[1].Value
}

Write-Host "Statistics:" -ForegroundColor Yellow
Write-Host "  Total JS files: $($allJsFiles.Count)" -ForegroundColor White
Write-Host "  Loaded files: $($loadedFiles.Count)" -ForegroundColor White

# Find unloaded files
$unloadedFiles = $allJsFiles | Where-Object { $_.Path -notin $loadedFiles }

Write-Host "  Unloaded files: $($unloadedFiles.Count)" -ForegroundColor Red
Write-Host ""

# Categorize unloaded files by directory
Write-Host "Unloaded files analysis:" -ForegroundColor Green

$categories = @{
    "backup" = @()
    "deployment" = @()
    "temp" = @()
    "tests" = @()
    "netlify" = @()
    "js/core" = @()
    "js/managers" = @()
    "js/ota-system" = @()
    "js/strategies" = @()
    "root" = @()
    "other" = @()
}

foreach ($file in $unloadedFiles) {
    $dir = $file.Directory
    $categorized = $false
    
    foreach ($category in $categories.Keys) {
        if ($dir -like "$category*" -or ($category -eq "root" -and $dir -eq "")) {
            $categories[$category] += $file
            $categorized = $true
            break
        }
    }
    
    if (-not $categorized) {
        $categories["other"] += $file
    }
}

# Output categorized results
foreach ($category in $categories.Keys) {
    if ($categories[$category].Count -gt 0) {
        Write-Host ""
        Write-Host "$category directory ($($categories[$category].Count) files):" -ForegroundColor Cyan

        foreach ($file in $categories[$category]) {
            $sizeKB = [math]::Round($file.Size / 1024, 1)
            $status = if ($file.Size -eq 0) { "[EMPTY]" }
                     elseif ($file.Name -like "*test*") { "[TEST]" }
                     elseif ($file.Name -like "*backup*") { "[BACKUP]" }
                     elseif ($file.Name -like "*temp*") { "[TEMP]" }
                     elseif ($file.Name -like "*diagnostic*") { "[DIAGNOSTIC]" }
                     else { "[UNKNOWN]" }

            Write-Host "  - $($file.Path) ($sizeKB KB) $status" -ForegroundColor White
        }
    }
}

Write-Host ""
Write-Host "Analysis completed!" -ForegroundColor Green
