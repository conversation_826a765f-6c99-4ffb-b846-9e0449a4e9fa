/**
 * 分析未加载的JavaScript文件
 * 对比项目中所有JS文件与index.html中的引用
 */

// 从index.html中提取的所有被引用的JS文件路径
const loadedFiles = [
    "js/core/dependency-container.js",
    "js/core/service-locator.js", 
    "js/core/ota-registry.js",
    "js/core/duplicate-checker.js",
    "js/core/architecture-guardian.js",
    "js/core/application-bootstrap.js",
    "js/core/global-event-coordinator.js",
    "js/core/component-lifecycle-manager.js",
    "js/core/unified-data-manager.js",
    "js/core/vehicle-configuration-manager.js",
    "js/core/vehicle-config-integration.js",
    "js/core/global-field-standardization-layer.js",
    "js/core/development-standards-guardian.js",
    "js/core/language-detector.js",
    "js/adapters/base-manager-adapter.js",
    "js/adapters/ota-manager-decorator.js",
    "js/core/ota-manager-factory.js",
    "js/core/feature-toggle.js",
    "js/core/shadow-deployment.js",
    "js/core/hot-rollback.js",
    "js/core/progressive-improvement-planner.js",
    "js/core/ota-bootstrap-integration.js",
    "js/shadow/fliggy-processor.js",
    "js/shadow/feature-toggle-bridge.js",
    "js/shadow/fliggy-shadow-sample-runner.js",
    "js/shadow/shadow-diff-reporter.js",
    "js/shadow/fliggy-price-processor.js",
    "js/shadow/price-feature-toggle-bridge.js",
    "js/shadow/fliggy-price-sample-runner.js",
    "js/core/interface-compatibility-validator.js",
    "js/core/auto-validation-runner.js",
    "js/utils.js",
    "js/logger.js",
    "js/monitoring-wrapper.js",
    "js/ota-channel-mapping.js",
    "js/hotel-name-database.js",
    "js/address-translation-service.js",
    "js/ota-system/ota-system-loader.js",
    "js/app-state.js",
    "js/language-manager.js",
    "js/api-service.js",
    "js/hotel-data-inline.js",
    "js/gemini-service.js",
    "js/order-history-manager.js",
    "js/image-upload-manager.js",
    "js/currency-converter.js",
    "js/flight-info-service.js",
    "js/multi-order/field-mapping-config.js",
    "js/multi-order/field-mapping-validator.js",
    "js/multi-order/multi-order-detector.js",
    "js/multi-order/multi-order-renderer.js",
    "js/multi-order/multi-order-processor.js",
    "js/multi-order/multi-order-transformer.js",
    "js/multi-order/field-mapping-tests.js",
    "js/multi-order/multi-order-state-manager.js",
    "js/multi-order/batch-processor.js",
    "js/multi-order/multi-order-coordinator.js",
    "js/multi-order-manager-v2.js",
    "js/multi-order/system-integrity-checker.js",
    "js/paging-service-manager.js",
    "js/grid-resizer.js",
    "js/i18n.js",
    "js/auto-resize-manager.js",
    "js/managers/form-manager.js",
    "js/managers/price-manager.js",
    "js/managers/event-manager.js",
    "js/managers/ui-state-manager.js",
    "js/managers/realtime-analysis-manager.js",
    "js/ui-manager.js",
    "main.js"
];

// 项目中发现的所有JS文件（从PowerShell扫描结果）
const allFiles = [
    // 根目录
    "main.js",
    "code-optimizer.js",
    
    // backup目录
    "backup/gemini-service.js",
    "backup/multi-order-manager.js", 
    "backup/verify-backup.js",
    
    // deployment目录
    "deployment/monitoring-setup.js",
    "deployment/netlify-diagnostic.js",
    "deployment/production-config.js",
    "deployment/validate-deployment.js",
    "deployment/website-diagnostic.js",
    
    // js目录 - 核心文件
    "js/address-translation-service.js",
    "js/api-service.js",
    "js/app-state.js",
    "js/auto-resize-manager.js",
    "js/currency-converter.js",
    "js/flight-info-service.js",
    "js/gemini-service.js",
    "js/grid-resizer.js",
    "js/hotel-data-inline.js",
    "js/hotel-name-database.js",
    "js/i18n.js",
    "js/image-upload-manager.js",
    "js/language-manager.js",
    "js/logger.js",
    "js/monitoring-wrapper.js",
    "js/multi-order-manager-v2.js",
    "js/order-history-manager.js",
    "js/ota-channel-mapping.js",
    "js/paging-service-manager.js",
    "js/single-order-dropdown-fix.js",
    "js/single-order-emergency-fix.js",
    "js/ui-manager.js",
    "js/utils.js",
    
    // js/adapters
    "js/adapters/base-manager-adapter.js",
    "js/adapters/ota-manager-decorator.js",
    
    // js/core
    "js/core/application-bootstrap.js",
    "js/core/architecture-guardian.js",
    "js/core/auto-validation-runner.js",
    "js/core/base-ota-strategy.js",
    "js/core/component-lifecycle-manager.js",
    "js/core/dependency-container.js",
    "js/core/development-standards-guardian.js",
    "js/core/duplicate-checker.js",
    "js/core/feature-toggle.js",
    "js/core/global-event-coordinator.js",
    "js/core/global-field-standardization-layer.js",
    "js/core/hot-rollback.js",
    "js/core/interface-compatibility-validator.js",
    "js/core/language-detector.js",
    "js/core/ota-bootstrap-integration.js",
    "js/core/ota-configuration-manager.js",
    "js/core/ota-event-bridge.js",
    "js/core/ota-manager-factory.js",
    "js/core/ota-registry.js",
    "js/core/ota-system-integrator.js",
    "js/core/progressive-improvement-planner.js",
    "js/core/service-locator.js",
    "js/core/shadow-deployment.js",
    "js/core/unified-data-manager.js",
    "js/core/vehicle-config-integration.js",
    "js/core/vehicle-configuration-manager.js"
];

console.log('开始分析未加载的JavaScript文件...');
