/**
 * 性能监控系统 - Linus重构版
 * 
 * 实时监控系统性能，收集关键指标
 * 遵循"测量才能改进"的原则
 */

'use strict';

class PerformanceMonitor {
    constructor(config = {}) {
        this.config = {
            enabled: true,
            sampleRate: 1.0, // 采样率 (0-1)
            reportInterval: 30000, // 30秒报告一次
            maxMetrics: 1000, // 最大存储指标数量
            endpoints: {
                metrics: '/api/metrics',
                errors: '/api/errors'
            },
            thresholds: {
                loadTime: 3000, // 3秒加载时间阈值
                apiResponseTime: 5000, // 5秒API响应阈值
                memoryUsage: 50 * 1024 * 1024, // 50MB内存阈值
                errorRate: 0.05 // 5%错误率阈值
            },
            ...config
        };

        this.metrics = [];
        this.errors = [];
        this.startTime = performance.now();
        this.isReporting = false;

        // 性能观察器
        this.observers = {};
        
        this.init();
    }

    init() {
        if (!this.config.enabled) return;

        console.log('🔍 启动性能监控系统...');

        // 监听页面加载性能
        this.trackPageLoad();
        
        // 监听API调用性能
        this.trackAPIPerformance();
        
        // 监听内存使用
        this.trackMemoryUsage();
        
        // 监听错误
        this.trackErrors();
        
        // 监听用户交互
        this.trackUserInteractions();
        
        // 监听网络状态
        this.trackNetworkStatus();
        
        // 开始定期报告
        this.startReporting();

        console.log('✅ 性能监控系统已启动');
    }

    // 页面加载性能监控
    trackPageLoad() {
        if ('PerformanceObserver' in window) {
            // 监听导航性能
            const navObserver = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    this.recordMetric({
                        type: 'navigation',
                        name: 'page_load',
                        duration: entry.loadEventEnd - entry.fetchStart,
                        domContentLoaded: entry.domContentLoadedEventEnd - entry.fetchStart,
                        firstPaint: this.getFirstPaint(),
                        timestamp: Date.now()
                    });
                });
            });
            navObserver.observe({ entryTypes: ['navigation'] });
            this.observers.navigation = navObserver;

            // 监听资源加载
            const resourceObserver = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    if (entry.name.includes('.js') || entry.name.includes('.css')) {
                        this.recordMetric({
                            type: 'resource',
                            name: entry.name.split('/').pop(),
                            duration: entry.responseEnd - entry.fetchStart,
                            size: entry.transferSize || entry.encodedBodySize,
                            timestamp: Date.now()
                        });
                    }
                });
            });
            resourceObserver.observe({ entryTypes: ['resource'] });
            this.observers.resource = resourceObserver;
        }

        // Web Vitals监控
        this.trackWebVitals();
    }

    // Web Vitals核心指标
    trackWebVitals() {
        // First Contentful Paint (FCP)
        this.observePerformanceEntry('paint', (entries) => {
            entries.forEach(entry => {
                if (entry.name === 'first-contentful-paint') {
                    this.recordMetric({
                        type: 'web_vital',
                        name: 'fcp',
                        value: entry.startTime,
                        timestamp: Date.now()
                    });
                }
            });
        });

        // Largest Contentful Paint (LCP)
        this.observePerformanceEntry('largest-contentful-paint', (entries) => {
            const lastEntry = entries[entries.length - 1];
            this.recordMetric({
                type: 'web_vital',
                name: 'lcp',
                value: lastEntry.startTime,
                timestamp: Date.now()
            });
        });

        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        this.observePerformanceEntry('layout-shift', (entries) => {
            entries.forEach(entry => {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            });
            this.recordMetric({
                type: 'web_vital',
                name: 'cls',
                value: clsValue,
                timestamp: Date.now()
            });
        });
    }

    // API性能监控
    trackAPIPerformance() {
        // 拦截fetch请求
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const startTime = performance.now();
            const url = args[0];
            
            try {
                const response = await originalFetch(...args);
                const endTime = performance.now();
                
                this.recordMetric({
                    type: 'api',
                    name: this.extractAPIEndpoint(url),
                    duration: endTime - startTime,
                    status: response.status,
                    success: response.ok,
                    timestamp: Date.now()
                });

                return response;
            } catch (error) {
                const endTime = performance.now();
                
                this.recordError({
                    type: 'api_error',
                    endpoint: this.extractAPIEndpoint(url),
                    duration: endTime - startTime,
                    error: error.message,
                    timestamp: Date.now()
                });

                throw error;
            }
        };
    }

    // 内存使用监控
    trackMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                this.recordMetric({
                    type: 'memory',
                    name: 'heap_usage',
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit,
                    percentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
                    timestamp: Date.now()
                });
            }, 10000); // 每10秒检查一次
        }
    }

    // 错误监控
    trackErrors() {
        // JavaScript错误
        window.addEventListener('error', (event) => {
            this.recordError({
                type: 'javascript_error',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                timestamp: Date.now()
            });
        });

        // Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.recordError({
                type: 'promise_rejection',
                reason: event.reason?.toString(),
                stack: event.reason?.stack,
                timestamp: Date.now()
            });
        });

        // 资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.recordError({
                    type: 'resource_error',
                    resource: event.target.src || event.target.href,
                    tagName: event.target.tagName,
                    timestamp: Date.now()
                });
            }
        }, true);
    }

    // 用户交互监控
    trackUserInteractions() {
        const interactions = ['click', 'input', 'scroll', 'resize'];
        
        interactions.forEach(eventType => {
            let lastTime = 0;
            document.addEventListener(eventType, (event) => {
                const now = Date.now();
                if (now - lastTime > 1000) { // 节流：1秒内只记录一次
                    this.recordMetric({
                        type: 'interaction',
                        name: eventType,
                        target: event.target?.tagName?.toLowerCase(),
                        timestamp: now
                    });
                    lastTime = now;
                }
            }, { passive: true });
        });
    }

    // 网络状态监控
    trackNetworkStatus() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            this.recordMetric({
                type: 'network',
                name: 'connection_info',
                effectiveType: connection.effectiveType,
                downlink: connection.downlink,
                rtt: connection.rtt,
                saveData: connection.saveData,
                timestamp: Date.now()
            });

            connection.addEventListener('change', () => {
                this.recordMetric({
                    type: 'network',
                    name: 'connection_change',
                    effectiveType: connection.effectiveType,
                    downlink: connection.downlink,
                    rtt: connection.rtt,
                    timestamp: Date.now()
                });
            });
        }

        // 在线/离线状态
        window.addEventListener('online', () => {
            this.recordMetric({
                type: 'network',
                name: 'status_change',
                status: 'online',
                timestamp: Date.now()
            });
        });

        window.addEventListener('offline', () => {
            this.recordMetric({
                type: 'network',
                name: 'status_change',
                status: 'offline',
                timestamp: Date.now()
            });
        });
    }

    // 记录性能指标
    recordMetric(metric) {
        if (Math.random() > this.config.sampleRate) return;

        this.metrics.push(metric);

        // 限制内存使用
        if (this.metrics.length > this.config.maxMetrics) {
            this.metrics.splice(0, this.metrics.length - this.config.maxMetrics);
        }

        // 检查阈值告警
        this.checkThresholds(metric);
    }

    // 记录错误
    recordError(error) {
        this.errors.push(error);
        
        // 限制内存使用
        if (this.errors.length > this.config.maxMetrics) {
            this.errors.splice(0, this.errors.length - this.config.maxMetrics);
        }

        // 立即报告严重错误
        if (this.shouldReportImmediately(error)) {
            this.reportError(error);
        }
    }

    // 阈值检查和告警
    checkThresholds(metric) {
        const { thresholds } = this.config;

        switch (metric.type) {
            case 'navigation':
                if (metric.duration > thresholds.loadTime) {
                    this.triggerAlert('slow_load', `页面加载时间超过阈值: ${metric.duration}ms`);
                }
                break;
            
            case 'api':
                if (metric.duration > thresholds.apiResponseTime) {
                    this.triggerAlert('slow_api', `API响应时间超过阈值: ${metric.name} ${metric.duration}ms`);
                }
                break;
            
            case 'memory':
                if (metric.used > thresholds.memoryUsage) {
                    this.triggerAlert('high_memory', `内存使用超过阈值: ${Math.round(metric.used / 1024 / 1024)}MB`);
                }
                break;
        }
    }

    // 触发告警
    triggerAlert(type, message) {
        console.warn(`🚨 性能告警 [${type}]: ${message}`);
        
        // 可以集成到告警系统
        this.recordMetric({
            type: 'alert',
            name: type,
            message,
            timestamp: Date.now()
        });
    }

    // 开始定期报告
    startReporting() {
        if (this.isReporting) return;
        
        this.isReporting = true;
        
        setInterval(() => {
            this.generateReport();
        }, this.config.reportInterval);
    }

    // 生成性能报告
    generateReport() {
        const report = {
            timestamp: Date.now(),
            session: {
                duration: Date.now() - this.startTime,
                userAgent: navigator.userAgent,
                url: window.location.href
            },
            metrics: this.getMetricsSummary(),
            errors: this.getErrorsSummary(),
            vitals: this.getWebVitalsSummary()
        };

        // 发送报告
        if (this.config.endpoints.metrics) {
            this.sendReport(report);
        }

        // 控制台输出摘要
        this.logSummary(report);
    }

    // 获取指标摘要
    getMetricsSummary() {
        const summary = {};
        
        this.metrics.forEach(metric => {
            const key = `${metric.type}_${metric.name}`;
            if (!summary[key]) {
                summary[key] = {
                    count: 0,
                    total: 0,
                    min: Infinity,
                    max: -Infinity
                };
            }
            
            const duration = metric.duration || metric.value || 0;
            summary[key].count++;
            summary[key].total += duration;
            summary[key].min = Math.min(summary[key].min, duration);
            summary[key].max = Math.max(summary[key].max, duration);
            summary[key].avg = summary[key].total / summary[key].count;
        });

        return summary;
    }

    // 获取错误摘要
    getErrorsSummary() {
        const errorsByType = {};
        
        this.errors.forEach(error => {
            if (!errorsByType[error.type]) {
                errorsByType[error.type] = 0;
            }
            errorsByType[error.type]++;
        });

        return {
            total: this.errors.length,
            byType: errorsByType,
            rate: this.errors.length / (this.metrics.length + this.errors.length)
        };
    }

    // 获取Web Vitals摘要
    getWebVitalsSummary() {
        const vitals = {};
        
        this.metrics.filter(m => m.type === 'web_vital').forEach(metric => {
            vitals[metric.name] = metric.value;
        });

        return vitals;
    }

    // 发送报告到服务器
    async sendReport(report) {
        try {
            await fetch(this.config.endpoints.metrics, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(report)
            });
        } catch (error) {
            console.error('性能报告发送失败:', error);
        }
    }

    // 控制台日志摘要
    logSummary(report) {
        console.group('📊 性能监控报告');
        console.log('⏱️ 会话时长:', Math.round(report.session.duration / 1000), '秒');
        console.log('📈 指标数量:', Object.keys(report.metrics).length);
        console.log('❌ 错误数量:', report.errors.total);
        console.log('🎯 Web Vitals:', report.vitals);
        console.groupEnd();
    }

    // 工具方法
    observePerformanceEntry(entryType, callback) {
        if ('PerformanceObserver' in window) {
            try {
                const observer = new PerformanceObserver((list) => {
                    callback(list.getEntries());
                });
                observer.observe({ entryTypes: [entryType] });
                this.observers[entryType] = observer;
            } catch (e) {
                console.warn(`不支持性能观察类型: ${entryType}`);
            }
        }
    }

    getFirstPaint() {
        const paintEntries = performance.getEntriesByType('paint');
        const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
        return firstPaint ? firstPaint.startTime : null;
    }

    extractAPIEndpoint(url) {
        try {
            const urlObj = new URL(url, window.location.origin);
            return urlObj.pathname.split('/').slice(0, 4).join('/'); // 前3级路径
        } catch {
            return url;
        }
    }

    shouldReportImmediately(error) {
        return error.type === 'javascript_error' || error.type === 'api_error';
    }

    async reportError(error) {
        try {
            await fetch(this.config.endpoints.errors, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(error)
            });
        } catch (e) {
            console.error('错误报告发送失败:', e);
        }
    }

    // 公共API
    getMetrics() {
        return this.metrics;
    }

    getErrors() {
        return this.errors;
    }

    destroy() {
        Object.values(this.observers).forEach(observer => {
            observer.disconnect();
        });
        this.isReporting = false;
    }
}

// 自动启动性能监控（生产环境）
if (typeof window !== 'undefined') {
    window.PerformanceMonitor = PerformanceMonitor;
    
    // 在生产环境自动启动
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
        window.performanceMonitor = new PerformanceMonitor();
    }
}

// Note: ES6 export removed to avoid syntax errors in non-module script context
// export default PerformanceMonitor;