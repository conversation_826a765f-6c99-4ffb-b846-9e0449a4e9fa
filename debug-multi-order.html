<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Order Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .debug-panel { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; border: 1px solid #ccc; background: #f8f9fa; border-radius: 4px; }
        button:hover { background: #e9ecef; }
        .dangerous { background: #dc3545; color: white; }
        .dangerous:hover { background: #c82333; }
        .log-output { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .test-data { background: #e7f3ff; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 Multi-Order System Debug Tool</h1>
    
    <div class="debug-panel">
        <h3>🚀 System Status</h3>
        <button onclick="checkSystemStatus()">Check System Status</button>
        <button onclick="forceReinitialization()">Force Re-initialization</button>
        <button onclick="clearAllData()" class="dangerous">Clear All Data</button>
        <div id="system-status"></div>
    </div>

    <div class="debug-panel">
        <h3>🧪 Test Multi-Order Functions</h3>
        <button onclick="testBasicFunctions()">Test Basic Functions</button>
        <button onclick="testWithMockData()">Test with Mock Data</button>
        <button onclick="testProcessingWorkflow()">Test Processing Workflow</button>
        <button onclick="simulateErrorConditions()">Simulate Error Conditions</button>
        <div id="test-results"></div>
    </div>

    <div class="debug-panel">
        <h3>📊 Performance Monitoring</h3>
        <button onclick="startPerformanceMonitoring()">Start Monitoring</button>
        <button onclick="stopPerformanceMonitoring()">Stop Monitoring</button>
        <button onclick="showMemoryUsage()">Show Memory Usage</button>
        <div id="performance-results"></div>
    </div>

    <div class="debug-panel">
        <h3>🐛 Console Log Monitor</h3>
        <button onclick="startLogCapture()">Start Log Capture</button>
        <button onclick="clearLogs()">Clear Logs</button>
        <div class="log-output" id="console-logs">Console logs will appear here...</div>
    </div>

    <div class="debug-panel">
        <h3>📝 Test Data</h3>
        <div class="test-data">
            <strong>Mock Multi-Order Data:</strong><br>
            <textarea id="mock-data" rows="4" style="width: 100%;">
订单1: 客户张三，从机场到酒店，2人，2023-10-20 14:00
订单2: 客户李四，从火车站到商场，3人，2023-10-20 15:30
订单3: 客户王五，从酒店到景点，4人，2023-10-20 16:00
            </textarea>
            <button onclick="processTestData()">Process Test Data</button>
        </div>
    </div>

    <!-- Load the actual scripts -->
    <script src="js/core/script-manifest.js"></script>
    <script src="js/core/script-loader.js"></script>

    <script>
        let performanceMonitoring = false;
        let logCapture = false;
        let originalConsole = {};

        function log(message, type = 'info', targetId = 'system-status') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            document.getElementById(targetId).appendChild(div);
        }

        function checkSystemStatus() {
            log('🔍 Checking system status...', 'info', 'system-status');
            
            // Clear previous results
            document.getElementById('system-status').innerHTML = '';
            
            // Check MultiOrder availability
            if (typeof window.MultiOrder !== 'undefined') {
                log('✅ MultiOrder module loaded', 'success', 'system-status');
                
                // Check initialization status
                try {
                    const state = window.MultiOrder.getState();
                    if (state.initialized) {
                        log('✅ MultiOrder initialized successfully', 'success', 'system-status');
                        log(`📊 Orders: ${state.orders.length}, UI Visible: ${state.visible}`, 'info', 'system-status');
                    } else {
                        log('⚠️ MultiOrder not initialized', 'warning', 'system-status');
                    }
                } catch (error) {
                    log('❌ Error getting MultiOrder state: ' + error.message, 'error', 'system-status');
                }
            } else {
                log('❌ MultiOrder module not available', 'error', 'system-status');
            }

            // Check dependencies
            const dependencies = [
                'OrderHistoryManager',
                'errorMonitor', 
                'performanceMonitor',
                'OTA'
            ];

            dependencies.forEach(dep => {
                if (typeof window[dep] !== 'undefined') {
                    log(`✅ ${dep} available`, 'success', 'system-status');
                } else {
                    log(`⚠️ ${dep} not available`, 'warning', 'system-status');
                }
            });
        }

        async function forceReinitialization() {
            log('🔄 Forcing re-initialization...', 'info', 'system-status');
            
            try {
                if (window.MultiOrder && window.MultiOrder.initialize) {
                    const result = await window.MultiOrder.initialize();
                    if (result) {
                        log('✅ Re-initialization successful', 'success', 'system-status');
                    } else {
                        log('❌ Re-initialization failed', 'error', 'system-status');
                    }
                } else {
                    log('❌ MultiOrder.initialize not available', 'error', 'system-status');
                }
            } catch (error) {
                log('❌ Re-initialization error: ' + error.message, 'error', 'system-status');
            }
        }

        function clearAllData() {
            if (confirm('⚠️ This will clear all multi-order data. Continue?')) {
                try {
                    localStorage.removeItem('multi_order_history');
                    
                    // Remove UI container
                    const container = document.getElementById('multi-order-container');
                    if (container) {
                        container.remove();
                    }
                    
                    log('✅ All data cleared', 'success', 'system-status');
                } catch (error) {
                    log('❌ Failed to clear data: ' + error.message, 'error', 'system-status');
                }
            }
        }

        function testBasicFunctions() {
            log('🧪 Testing basic functions...', 'info', 'test-results');
            document.getElementById('test-results').innerHTML = '';
            
            const tests = [
                () => typeof window.MultiOrder !== 'undefined',
                () => typeof window.MultiOrder.initialize === 'function',
                () => typeof window.MultiOrder.detect === 'function',
                () => typeof window.MultiOrder.process === 'function',
                () => typeof window.MultiOrder.showUI === 'function',
                () => typeof window.MultiOrder.hideUI === 'function'
            ];

            const testNames = [
                'MultiOrder object exists',
                'initialize method exists',
                'detect method exists', 
                'process method exists',
                'showUI method exists',
                'hideUI method exists'
            ];

            tests.forEach((test, index) => {
                try {
                    if (test()) {
                        log(`✅ ${testNames[index]}`, 'success', 'test-results');
                    } else {
                        log(`❌ ${testNames[index]}`, 'error', 'test-results');
                    }
                } catch (error) {
                    log(`❌ ${testNames[index]}: ${error.message}`, 'error', 'test-results');
                }
            });
        }

        async function testWithMockData() {
            log('🧪 Testing with mock data...', 'info', 'test-results');
            
            try {
                if (!window.MultiOrder) {
                    throw new Error('MultiOrder not available');
                }

                const mockOrders = [
                    { id: 'test1', content: '测试订单1', selected: true },
                    { id: 'test2', content: '测试订单2', selected: false },
                    { id: 'test3', content: '测试订单3', selected: true }
                ];

                // Test processing
                const processed = window.MultiOrder.process(mockOrders);
                log(`✅ Process test: ${processed.length} orders processed`, 'success', 'test-results');

                // Test UI
                const uiResult = window.MultiOrder.showUI(processed);
                if (uiResult) {
                    log('✅ UI test: Successfully showed UI', 'success', 'test-results');
                    
                    // Hide UI after 2 seconds
                    setTimeout(() => {
                        window.MultiOrder.hideUI();
                        log('✅ UI test: Successfully hid UI', 'success', 'test-results');
                    }, 2000);
                } else {
                    log('❌ UI test: Failed to show UI', 'error', 'test-results');
                }

            } catch (error) {
                log('❌ Mock data test failed: ' + error.message, 'error', 'test-results');
            }
        }

        function processTestData() {
            const testData = document.getElementById('mock-data').value;
            log('📝 Processing custom test data...', 'info', 'test-results');
            
            try {
                if (!window.MultiOrder) {
                    throw new Error('MultiOrder not available');
                }

                // Detect multi-order content
                const detection = window.MultiOrder.detect(testData);
                log(`🔍 Detection result: ${detection.isMultiOrder ? 'Multi-order detected' : 'No multi-order detected'} (confidence: ${detection.confidence})`, 'info', 'test-results');

                if (detection.orders && detection.orders.length > 0) {
                    // Process and show
                    const processed = window.MultiOrder.process(detection.orders);
                    window.MultiOrder.showUI(processed);
                    log(`✅ Processed ${processed.length} orders from test data`, 'success', 'test-results');
                } else {
                    log('⚠️ No orders found in test data', 'warning', 'test-results');
                }

            } catch (error) {
                log('❌ Test data processing failed: ' + error.message, 'error', 'test-results');
            }
        }

        function startLogCapture() {
            if (logCapture) return;
            
            logCapture = true;
            originalConsole.log = console.log;
            originalConsole.error = console.error;
            originalConsole.warn = console.warn;

            const logContainer = document.getElementById('console-logs');
            
            console.log = function(...args) {
                originalConsole.log(...args);
                logContainer.innerHTML += `<div style="color: #333">[LOG] ${args.join(' ')}</div>`;
                logContainer.scrollTop = logContainer.scrollHeight;
            };

            console.error = function(...args) {
                originalConsole.error(...args);
                logContainer.innerHTML += `<div style="color: #dc3545">[ERROR] ${args.join(' ')}</div>`;
                logContainer.scrollTop = logContainer.scrollHeight;
            };

            console.warn = function(...args) {
                originalConsole.warn(...args);
                logContainer.innerHTML += `<div style="color: #856404">[WARN] ${args.join(' ')}</div>`;
                logContainer.scrollTop = logContainer.scrollHeight;
            };

            log('📡 Log capture started', 'success', 'performance-results');
        }

        function clearLogs() {
            document.getElementById('console-logs').innerHTML = 'Console logs cleared...';
        }

        // Auto-start basic checks when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('🚀 Debug tool loaded, starting basic checks...', 'info', 'system-status');
                checkSystemStatus();
            }, 2000);
        });
    </script>
</body>
</html>
