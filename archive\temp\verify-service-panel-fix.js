/**
 * 验证服务配置面板修复效果
 * Chrome MCP验证脚本 - 在浏览器控制台运行
 */

console.log('🔍 === 验证服务配置面板修复效果 ===');
console.log('修复内容:');
console.log('- Z-index层级提升至1001');  
console.log('- 背景不透明度提升至95%');
console.log('- 添加紫色边框和彩色阴影');
console.log('- 智能定位算法增强避让');
console.log('=====================================\n');

// 验证Z-index修复
function verifyZIndexFix() {
    console.log('1️⃣ === Z-index层级验证 ===');
    
    const servicePanel = document.querySelector('[data-panel="service-config"]');
    if (!servicePanel) {
        console.error('❌ 服务配置面板不存在！');
        return false;
    }
    
    const styles = getComputedStyle(servicePanel);
    const zIndex = styles.zIndex;
    const position = styles.position;
    
    console.log('Z-index值:', zIndex);
    console.log('Position值:', position);
    
    const zIndexNumber = parseInt(zIndex);
    const success = zIndexNumber >= 1001;
    
    console.log(`Z-index检验: ${success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`预期: >= 1001, 实际: ${zIndex}`);
    
    return success;
}

// 验证背景增强修复
function verifyBackgroundFix() {
    console.log('\n2️⃣ === 背景增强验证 ===');
    
    const servicePanel = document.querySelector('[data-panel="service-config"]');
    if (!servicePanel) {
        console.error('❌ 服务配置面板不存在！');
        return false;
    }
    
    const styles = getComputedStyle(servicePanel);
    console.log('背景颜色:', styles.backgroundColor);
    console.log('边框:', styles.border);
    console.log('盒阴影:', styles.boxShadow);
    console.log('毛玻璃效果:', styles.backdropFilter);
    
    // 检查背景不透明度
    const bgColor = styles.backgroundColor;
    const hasHighOpacity = bgColor.includes('0.95') || bgColor.includes('rgba(255, 255, 255, 0.95)');
    
    // 检查边框
    const hasBorder = styles.border.includes('2px') && styles.border.includes('rgb(159, 41, 159)');
    
    // 检查阴影
    const hasShadow = styles.boxShadow && styles.boxShadow !== 'none' && styles.boxShadow.includes('rgba');
    
    console.log(`背景不透明度: ${hasHighOpacity ? '✅ 已提升' : '❌ 未提升'}`);
    console.log(`紫色边框: ${hasBorder ? '✅ 已添加' : '❌ 未添加'}`);
    console.log(`彩色阴影: ${hasShadow ? '✅ 已添加' : '❌ 未添加'}`);
    
    return hasHighOpacity && hasBorder && hasShadow;
}

// 验证可见性
function verifyVisibility() {
    console.log('\n3️⃣ === 可见性验证 ===');
    
    const servicePanel = document.querySelector('[data-panel="service-config"]');
    if (!servicePanel) {
        console.error('❌ 服务配置面板不存在！');
        return false;
    }
    
    const rect = servicePanel.getBoundingClientRect();
    const styles = getComputedStyle(servicePanel);
    
    console.log('显示状态:', styles.display);
    console.log('可见性:', styles.visibility);  
    console.log('透明度:', styles.opacity);
    console.log('位置:', `(${rect.left.toFixed(1)}, ${rect.top.toFixed(1)})`);
    console.log('尺寸:', `${rect.width.toFixed(1)} × ${rect.height.toFixed(1)}`);
    
    const isVisible = styles.display !== 'none' && 
                     styles.visibility !== 'hidden' && 
                     parseFloat(styles.opacity) > 0.8 &&
                     rect.width > 0 && rect.height > 0;
    
    console.log(`可见性检验: ${isVisible ? '✅ 通过' : '❌ 失败'}`);
    
    return isVisible;
}

// 测试遮挡情况（修复后）
function testOverlapAfterFix() {
    console.log('\n4️⃣ === 遮挡情况测试 ===');
    
    const servicePanel = document.querySelector('[data-panel="service-config"]');
    const dropdown = document.querySelector('#languagesDropdown');
    const trigger = document.querySelector('#languagesTrigger');
    const options = document.querySelector('#languagesOptions');
    
    if (!servicePanel || !trigger || !options) {
        console.error('❌ 必要元素不存在');
        return false;
    }
    
    console.log('模拟打开下拉菜单...');
    
    // 模拟点击打开下拉菜单
    trigger.click();
    
    setTimeout(() => {
        const servicePanelRect = servicePanel.getBoundingClientRect();
        const optionsRect = options.getBoundingClientRect();
        const servicePanelStyles = getComputedStyle(servicePanel);
        const optionsStyles = getComputedStyle(options);
        
        console.log('服务面板Z-index:', servicePanelStyles.zIndex);
        console.log('下拉菜单Z-index:', optionsStyles.zIndex);
        console.log('服务面板位置:', `(${servicePanelRect.left.toFixed(1)}, ${servicePanelRect.top.toFixed(1)})`);
        console.log('下拉菜单位置:', `(${optionsRect.left.toFixed(1)}, ${optionsRect.top.toFixed(1)})`);
        
        // Z-index层级比较
        const servicePanelZ = parseInt(servicePanelStyles.zIndex);
        const optionsZ = parseInt(optionsStyles.zIndex);
        const layerCorrect = servicePanelZ > optionsZ;
        
        console.log(`层级关系: ${layerCorrect ? '✅ 服务面板在上层' : '❌ 层级有问题'}`);
        console.log(`服务面板(${servicePanelZ}) vs 下拉菜单(${optionsZ})`);
        
        // 检查几何重叠（物理遮挡）
        const isOverlapping = !(servicePanelRect.right < optionsRect.left || 
                              servicePanelRect.left > optionsRect.right || 
                              servicePanelRect.bottom < optionsRect.top || 
                              servicePanelRect.top > optionsRect.bottom);
        
        if (isOverlapping) {
            console.log('⚠️ 几何位置仍有重叠，但Z-index层级确保服务面板显示在上层');
        } else {
            console.log('✅ 无几何重叠，位置完全分离');
        }
        
        // 关闭下拉菜单
        document.addEventListener('click', () => {
            options.classList.remove('show');
        }, { once: true });
        
        document.body.click();
        
        return layerCorrect;
        
    }, 200);
}

// 生成修复验证报告
function generateVerificationReport() {
    console.log('\n5️⃣ === 修复验证报告 ===');
    
    const zIndexResult = verifyZIndexFix();
    const backgroundResult = verifyBackgroundFix(); 
    const visibilityResult = verifyVisibility();
    
    console.log('\n📊 修复效果总结:');
    console.log(`Z-index层级修复: ${zIndexResult ? '✅ 成功' : '❌ 失败'}`);
    console.log(`背景增强修复: ${backgroundResult ? '✅ 成功' : '❌ 失败'}`);
    console.log(`可见性验证: ${visibilityResult ? '✅ 成功' : '❌ 失败'}`);
    
    const overallSuccess = zIndexResult && backgroundResult && visibilityResult;
    
    console.log(`\n🎯 整体修复结果: ${overallSuccess ? '🎉 完全成功' : '⚠️ 需要进一步调试'}`);
    
    if (overallSuccess) {
        console.log('\n✨ 修复完成！服务配置面板现在应该完全可见');
        console.log('- Z-index层级正确，不会被下拉菜单遮挡');
        console.log('- 背景增强，内容清晰可读');
        console.log('- 紫色边框和阴影增强视觉效果');
    } else {
        console.log('\n🔧 仍需解决的问题:');
        if (!zIndexResult) console.log('- Z-index层级设置未生效');
        if (!backgroundResult) console.log('- 背景增强样式未应用');
        if (!visibilityResult) console.log('- 元素可见性异常');
    }
    
    return overallSuccess;
}

// 主验证函数
function runVerification() {
    console.clear();
    console.log('🔍 === 开始验证服务配置面板修复 ===');
    console.log('时间:', new Date().toLocaleString());
    console.log('页面:', window.location.href);
    
    const result = generateVerificationReport();
    
    setTimeout(() => {
        testOverlapAfterFix();
        
        console.log('\n🏁 === 验证完成 ===');
        if (result) {
            console.log('🎊 恭喜！服务配置面板显示问题已完全解决');
        } else {
            console.log('🔧 修复未完全生效，请检查CSS是否正确加载');
        }
    }, 1000);
    
    return result;
}

// 暴露验证函数
window.verifyServicePanelFix = {
    runVerification,
    verifyZIndexFix,
    verifyBackgroundFix,
    verifyVisibility,
    testOverlapAfterFix,
    generateVerificationReport
};

// 自动运行验证
console.log('🚀 自动运行修复验证...');
console.log('如需手动控制，使用: window.verifyServicePanelFix.functionName()');
runVerification();