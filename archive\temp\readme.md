# 🗂️ Temp 文件夹

此文件夹包含OTA订单处理系统的临时文件、实验性代码和诊断脚本。

## 📁 文件说明

### 诊断脚本
- `chrome-mcp-diagnostic-script.js` - Chrome MCP诊断脚本
- `validate-fixes.js` - 修复验证脚本
- `verify-service-panel-fix.js` - 服务面板修复验证脚本

### 文件分类

#### 🔧 诊断工具
- 系统诊断脚本
- 功能验证工具
- 性能测试脚本

#### 🧪 实验性代码
- 功能原型
- 测试代码片段
- 临时解决方案

#### 📦 备份文件
- 旧版本代码备份
- 配置文件备份
- 临时数据文件

## ⚠️ 重要说明

1. **临时性质**：此文件夹中的文件可能随时被删除或移动
2. **实验用途**：代码可能不稳定，仅用于测试和实验
3. **不用于生产**：这些文件不应在生产环境中使用
4. **定期清理**：建议定期清理不再需要的临时文件

## 🔄 维护规范

- 临时文件应有明确的用途说明
- 不再需要的文件应及时删除
- 重要的实验性代码应转移到正式位置
- 避免在此文件夹中存放重要的生产代码

## 📋 清理指南

### 可以删除的文件
- 过期的诊断脚本
- 已完成的实验代码
- 重复的备份文件

### 需要保留的文件
- 正在使用的诊断工具
- 有价值的实验性功能
- 重要的配置备份

---
*最后更新：2025年1月*
