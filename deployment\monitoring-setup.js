/**
 * 智能学习型格式预处理引擎 - 监控设置
 * 配置系统监控、日志收集、报警机制
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 监控配置
const MONITORING_CONFIG = {
    // 基础配置
    enabled: true,
    environment: 'production',
    version: '1.0.0',
    
    // 数据收集配置
    collection: {
        interval: 30000,        // 30秒收集一次
        batchSize: 100,         // 批量发送大小
        retryAttempts: 3,       // 重试次数
        retryDelay: 5000,       // 重试延迟
        enableCompression: true, // 启用数据压缩
        enableEncryption: false  // 数据加密（需要配置密钥）
    },
    
    // 指标配置
    metrics: {
        // 性能指标
        performance: {
            enabled: true,
            responseTime: true,
            memoryUsage: true,
            cpuUsage: false,      // 浏览器环境无法获取
            networkLatency: true,
            errorRate: true
        },
        
        // 业务指标
        business: {
            enabled: true,
            operationCount: true,
            learningAccuracy: true,
            cacheHitRate: true,
            ruleGenerationRate: true,
            userInteractions: true
        },
        
        // 系统指标
        system: {
            enabled: true,
            browserInfo: true,
            screenResolution: true,
            connectionType: true,
            storageUsage: true,
            moduleLoadTime: true
        }
    },
    
    // 报警配置
    alerts: {
        enabled: true,
        
        // 报警阈值
        thresholds: {
            responseTime: 5000,           // 5秒
            memoryUsage: 150 * 1024 * 1024, // 150MB
            errorRate: 0.05,              // 5%
            cacheHitRate: 0.6,            // 60%
            learningAccuracy: 0.7,        // 70%
            storageUsage: 0.9             // 90%
        },
        
        // 报警频率限制
        rateLimit: {
            enabled: true,
            maxAlertsPerHour: 10,
            cooldownPeriod: 300000  // 5分钟冷却期
        },
        
        // 报警渠道
        channels: {
            console: {
                enabled: true,
                level: 'warning'
            },
            storage: {
                enabled: true,
                maxAlerts: 1000
            },
            webhook: {
                enabled: false,
                url: null,
                headers: {},
                timeout: 10000
            },
            email: {
                enabled: false,
                recipients: [],
                smtp: null
            }
        }
    },
    
    // 日志配置
    logging: {
        enabled: true,
        level: 'info',
        maxLogSize: 5 * 1024 * 1024,  // 5MB
        rotationCount: 5,
        enableRemoteLogging: false,
        
        // 日志类别
        categories: {
            system: true,
            performance: true,
            business: true,
            error: true,
            security: true,
            debug: false
        }
    },
    
    // 健康检查配置
    healthCheck: {
        enabled: true,
        interval: 60000,        // 1分钟
        timeout: 5000,          // 5秒
        endpoints: [
            {
                name: 'learning-system',
                check: () => window.OTA?.learningConfig !== undefined
            },
            {
                name: 'cache-system',
                check: () => window.OTA?.intelligentCacheManager !== undefined
            }
            // performance-monitor 已移除
            // {
            //     name: 'performance-monitor',
            //     check: () => window.OTA?.performanceMonitor !== undefined
            // }
        ]
    }
};

/**
 * 监控系统类
 */
class MonitoringSystem {
    constructor(config = MONITORING_CONFIG) {
        this.config = config;
        this.isRunning = false;
        this.metrics = new Map();
        this.alerts = [];
        this.lastAlertTime = new Map();
        this.healthStatus = 'unknown';
        
        // 获取系统模块
        // this.performanceMonitor = window.OTA?.performanceMonitor; // 已移除
        this.cacheManager = window.OTA?.intelligentCacheManager;
        this.learningEvaluator = window.OTA?.learningEffectivenessEvaluator;
        this.logger = window.OTA?.logger;
        
        this.initialize();
    }

    /**
     * 初始化监控系统
     */
    initialize() {
        if (!this.config.enabled) {
            console.log('监控系统已禁用');
            return;
        }
        
        try {
            // 收集系统信息
            this.collectSystemInfo();
            
            // 启动定期收集
            this.startMetricsCollection();
            
            // 启动健康检查
            this.startHealthCheck();
            
            // 设置错误监听
            this.setupErrorHandling();
            
            this.isRunning = true;
            console.log('监控系统已启动', { version: this.config.version });
            
        } catch (error) {
            console.error('监控系统初始化失败:', error);
        }
    }

    /**
     * 收集系统信息
     */
    collectSystemInfo() {
        const systemInfo = {
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth
            },
            window: {
                innerWidth: window.innerWidth,
                innerHeight: window.innerHeight
            },
            connection: navigator.connection ? {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt
            } : null,
            memory: performance.memory ? {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
            } : null
        };
        
        this.metrics.set('systemInfo', systemInfo);
        this.log('系统信息收集完成', 'info', systemInfo);
    }

    /**
     * 启动指标收集
     */
    startMetricsCollection() {
        setInterval(() => {
            this.collectMetrics();
        }, this.config.collection.interval);
        
        this.log('指标收集已启动', 'info');
    }

    /**
     * 收集指标
     */
    collectMetrics() {
        try {
            const timestamp = Date.now();
            const metrics = {
                timestamp,
                performance: this.collectPerformanceMetrics(),
                business: this.collectBusinessMetrics(),
                system: this.collectSystemMetrics()
            };
            
            // 存储指标
            this.storeMetrics(metrics);
            
            // 检查报警条件
            this.checkAlertConditions(metrics);
            
        } catch (error) {
            this.log('指标收集失败', 'error', { error: error.message });
        }
    }

    /**
     * 收集性能指标
     */
    collectPerformanceMetrics() {
        const metrics = {};
        
        if (this.config.metrics.performance.enabled) {
            // 内存使用
            if (performance.memory && this.config.metrics.performance.memoryUsage) {
                metrics.memoryUsage = performance.memory.usedJSHeapSize;
            }
            
            // 响应时间 - 性能监控器已移除
            // if (this.performanceMonitor && this.config.metrics.performance.responseTime) {
            //     const realTimeMetrics = this.performanceMonitor.getRealTimeMetrics();
            //     metrics.responseTime = realTimeMetrics?.averageResponseTime || 0;
            // }
            
            // 错误率 - 性能监控器已移除
            // if (this.performanceMonitor && this.config.metrics.performance.errorRate) {
            //     const realTimeMetrics = this.performanceMonitor.getRealTimeMetrics();
            //     if (realTimeMetrics && realTimeMetrics.totalOperations > 0) {
            //         metrics.errorRate = realTimeMetrics.totalErrors / realTimeMetrics.totalOperations;
            //     }
            // }
            
            // 网络延迟
            if (this.config.metrics.performance.networkLatency) {
                metrics.networkLatency = navigator.connection?.rtt || 0;
            }
        }
        
        return metrics;
    }

    /**
     * 收集业务指标
     */
    collectBusinessMetrics() {
        const metrics = {};
        
        if (this.config.metrics.business.enabled) {
            // 操作数量
            if (this.performanceMonitor && this.config.metrics.business.operationCount) {
                const realTimeMetrics = this.performanceMonitor.getRealTimeMetrics();
                metrics.operationCount = realTimeMetrics?.totalOperations || 0;
            }
            
            // 学习准确率
            if (this.learningEvaluator && this.config.metrics.business.learningAccuracy) {
                const evaluation = this.learningEvaluator.getCurrentEvaluation();
                metrics.learningAccuracy = evaluation?.overallScore || 0;
            }
            
            // 缓存命中率
            if (this.cacheManager && this.config.metrics.business.cacheHitRate) {
                const cacheStats = this.cacheManager.getStats();
                metrics.cacheHitRate = cacheStats.hitRate || 0;
            }
            
            // 规则生成率
            if (window.OTA?.ruleGenerationEngine && this.config.metrics.business.ruleGenerationRate) {
                const rules = window.OTA.ruleGenerationEngine.getAllRules();
                metrics.ruleCount = rules.length;
            }
        }
        
        return metrics;
    }

    /**
     * 收集系统指标
     */
    collectSystemMetrics() {
        const metrics = {};
        
        if (this.config.metrics.system.enabled) {
            // 存储使用情况
            if (this.config.metrics.system.storageUsage) {
                try {
                    const estimate = navigator.storage?.estimate();
                    if (estimate) {
                        estimate.then(quota => {
                            metrics.storageUsage = quota.usage / quota.quota;
                        });
                    }
                } catch (error) {
                    // 忽略存储API错误
                }
            }
            
            // 连接类型
            if (this.config.metrics.system.connectionType) {
                metrics.connectionType = navigator.connection?.effectiveType || 'unknown';
            }
            
            // 在线状态
            metrics.onlineStatus = navigator.onLine;
        }
        
        return metrics;
    }

    /**
     * 存储指标
     */
    storeMetrics(metrics) {
        const key = `metrics_${metrics.timestamp}`;
        this.metrics.set(key, metrics);
        
        // 限制内存中的指标数量
        if (this.metrics.size > 1000) {
            const oldestKey = this.metrics.keys().next().value;
            this.metrics.delete(oldestKey);
        }
        
        // 存储到本地存储（可选）
        // 存储最新指标到localStorage（简化版）
        if (typeof Storage !== 'undefined') {
            try {
                localStorage.setItem('ota_latest_metrics', JSON.stringify(metrics));
            } catch (error) {
                // 忽略存储错误
            }
        }
    }

    /**
     * 检查报警条件
     */
    checkAlertConditions(metrics) {
        if (!this.config.alerts.enabled) {
            return;
        }
        
        const thresholds = this.config.alerts.thresholds;
        
        // 检查响应时间
        if (metrics.performance.responseTime > thresholds.responseTime) {
            this.triggerAlert('high_response_time', {
                current: metrics.performance.responseTime,
                threshold: thresholds.responseTime
            });
        }
        
        // 检查内存使用
        if (metrics.performance.memoryUsage > thresholds.memoryUsage) {
            this.triggerAlert('high_memory_usage', {
                current: metrics.performance.memoryUsage,
                threshold: thresholds.memoryUsage
            });
        }
        
        // 检查错误率
        if (metrics.performance.errorRate > thresholds.errorRate) {
            this.triggerAlert('high_error_rate', {
                current: metrics.performance.errorRate,
                threshold: thresholds.errorRate
            });
        }
        
        // 检查缓存命中率
        if (metrics.business.cacheHitRate < thresholds.cacheHitRate) {
            this.triggerAlert('low_cache_hit_rate', {
                current: metrics.business.cacheHitRate,
                threshold: thresholds.cacheHitRate
            });
        }
        
        // 检查学习准确率
        if (metrics.business.learningAccuracy < thresholds.learningAccuracy) {
            this.triggerAlert('low_learning_accuracy', {
                current: metrics.business.learningAccuracy,
                threshold: thresholds.learningAccuracy
            });
        }
    }

    /**
     * 触发报警
     */
    triggerAlert(alertType, data) {
        // 检查频率限制
        if (this.isAlertRateLimited(alertType)) {
            return;
        }
        
        const alert = {
            type: alertType,
            timestamp: Date.now(),
            data: data,
            severity: this.getAlertSeverity(alertType),
            environment: this.config.environment
        };
        
        // 记录报警时间
        this.lastAlertTime.set(alertType, alert.timestamp);
        
        // 存储报警
        this.alerts.push(alert);
        
        // 限制报警数量
        if (this.alerts.length > this.config.alerts.channels.storage.maxAlerts) {
            this.alerts.shift();
        }
        
        // 发送报警
        this.sendAlert(alert);
        
        this.log('报警触发', 'warning', alert);
    }

    /**
     * 检查报警频率限制
     */
    isAlertRateLimited(alertType) {
        if (!this.config.alerts.rateLimit.enabled) {
            return false;
        }
        
        const lastTime = this.lastAlertTime.get(alertType);
        if (!lastTime) {
            return false;
        }
        
        const timeSinceLastAlert = Date.now() - lastTime;
        return timeSinceLastAlert < this.config.alerts.rateLimit.cooldownPeriod;
    }

    /**
     * 发送报警
     */
    sendAlert(alert) {
        const channels = this.config.alerts.channels;
        
        // 控制台报警
        if (channels.console.enabled) {
            const message = `[${alert.severity.toUpperCase()}] ${alert.type}: ${JSON.stringify(alert.data)}`;
            
            switch (alert.severity) {
                case 'critical':
                    console.error(message);
                    break;
                case 'warning':
                    console.warn(message);
                    break;
                default:
                    console.info(message);
            }
        }
        
        // Webhook报警
        if (channels.webhook.enabled && channels.webhook.url) {
            this.sendWebhookAlert(alert);
        }
        
        // 邮件报警
        if (channels.email.enabled && channels.email.recipients.length > 0) {
            this.sendEmailAlert(alert);
        }
    }

    /**
     * 获取报警严重程度
     */
    getAlertSeverity(alertType) {
        const severityMap = {
            'high_response_time': 'warning',
            'high_memory_usage': 'warning',
            'high_error_rate': 'critical',
            'low_cache_hit_rate': 'info',
            'low_learning_accuracy': 'warning',
            'system_error': 'critical',
            'health_check_failed': 'critical'
        };
        
        return severityMap[alertType] || 'info';
    }

    /**
     * 启动健康检查
     */
    startHealthCheck() {
        if (!this.config.healthCheck.enabled) {
            return;
        }
        
        setInterval(() => {
            this.performHealthCheck();
        }, this.config.healthCheck.interval);
        
        this.log('健康检查已启动', 'info');
    }

    /**
     * 执行健康检查
     */
    performHealthCheck() {
        try {
            const healthResults = [];
            
            for (const endpoint of this.config.healthCheck.endpoints) {
                const startTime = Date.now();
                const isHealthy = endpoint.check();
                const responseTime = Date.now() - startTime;
                
                healthResults.push({
                    name: endpoint.name,
                    healthy: isHealthy,
                    responseTime: responseTime
                });
            }
            
            const overallHealth = healthResults.every(result => result.healthy);
            this.healthStatus = overallHealth ? 'healthy' : 'unhealthy';
            
            // 如果健康状况不佳，触发报警
            if (!overallHealth) {
                this.triggerAlert('health_check_failed', {
                    results: healthResults,
                    overallHealth: this.healthStatus
                });
            }
            
            this.log('健康检查完成', 'info', {
                status: this.healthStatus,
                results: healthResults
            });
            
        } catch (error) {
            this.healthStatus = 'error';
            this.log('健康检查失败', 'error', { error: error.message });
        }
    }

    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        // 全局错误监听
        window.addEventListener('error', (event) => {
            this.triggerAlert('system_error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });
        
        // Promise拒绝监听
        window.addEventListener('unhandledrejection', (event) => {
            this.triggerAlert('system_error', {
                type: 'unhandled_promise_rejection',
                reason: event.reason
            });
        });
    }

    /**
     * 发送Webhook报警
     */
    sendWebhookAlert(alert) {
        const config = this.config.alerts.channels.webhook;
        
        fetch(config.url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...config.headers
            },
            body: JSON.stringify(alert),
            signal: AbortSignal.timeout(config.timeout || 10000)
        }).catch(error => {
            this.log('Webhook报警发送失败', 'error', { error: error.message });
        });
    }

    /**
     * 发送邮件报警
     */
    sendEmailAlert(alert) {
        // 邮件发送需要后端支持，这里只是占位符
        this.log('邮件报警功能需要后端支持', 'warning', alert);
    }

    /**
     * 记录日志
     */
    log(message, level = 'info', data = null) {
        if (this.logger) {
            this.logger.log(message, level, data);
        } else {
            console.log(`[${level.toUpperCase()}] ${message}`, data);
        }
    }

    /**
     * 获取监控状态
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            healthStatus: this.healthStatus,
            metricsCount: this.metrics.size,
            alertsCount: this.alerts.length,
            lastMetricsTime: this.metrics.size > 0 ? 
                Array.from(this.metrics.values()).pop().timestamp : null
        };
    }

    /**
     * 获取最新指标
     */
    getLatestMetrics() {
        if (this.metrics.size === 0) {
            return null;
        }
        
        return Array.from(this.metrics.values()).pop();
    }

    /**
     * 获取报警历史
     */
    getAlerts(limit = 50) {
        return this.alerts.slice(-limit);
    }

    /**
     * 停止监控
     */
    stop() {
        this.isRunning = false;
        this.log('监控系统已停止', 'info');
    }
}

// 创建全局监控实例
const monitoringSystem = new MonitoringSystem();

// 导出到全局命名空间
window.OTA = window.OTA || {};
window.OTA.monitoringSystem = monitoringSystem;
window.OTA.MONITORING_CONFIG = MONITORING_CONFIG;

// 向后兼容
window.monitoringSystem = monitoringSystem;

console.log('监控系统已加载', { version: MONITORING_CONFIG.version });
