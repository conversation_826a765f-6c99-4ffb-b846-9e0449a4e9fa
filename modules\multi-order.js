/**
 * Multi-Order Module - The Right Way
 * 
 * Clean, simple, no bullshit architecture.
 * One file, all functionality, zero unnecessary abstractions.
 */

const MultiOrder = (() => {
    'use strict';
    
    // State - keep it simple
    let isInitialized = false;
    let container = null;
    let currentOrders = [];
    let historyManager = null;
    
    // Detection patterns - hardcoded, no config hell
    const PATTERNS = [
        /多个.*?订单/i,
        /(\d+)\s*个.*?订单/i,
        /批量.*?订单/i,
        /订单.*?(\d+).*?订单.*?(\d+)/i,
        /order.*?\d+.*?order.*?\d+/i
    ];

    // Initialize - do it once, do it right - FIXED: Added safety checks
    const initialize = async () => {
        if (isInitialized) {
            console.log('MultiOrder already initialized');
            return true;
        }
        
        try {
            console.log('MultiOrder initializing...');
            
            // Find history manager - simple fallback chain with safety checks
            try {
                historyManager = window.OrderHistoryManager || 
                               window.orderHistoryManager || 
                               createSimpleHistory();
            } catch (historyError) {
                console.warn('History manager setup failed, using fallback:', historyError);
                historyManager = createSimpleHistory();
            }
            
            // Create UI container if needed - with safety checks
            try {
                container = document.getElementById('multi-order-container');
                if (!container) {
                    container = document.createElement('div');
                    container.id = 'multi-order-container';
                    container.className = 'multi-order-module';
                    
                    // Ensure document.body exists
                    if (document.body) {
                        document.body.appendChild(container);
                    } else {
                        // If body not ready, wait and try again
                        setTimeout(() => {
                            if (document.body && !document.getElementById('multi-order-container')) {
                                document.body.appendChild(container);
                            }
                        }, 100);
                    }
                }
            } catch (containerError) {
                console.error('Container creation failed:', containerError);
                return false;
            }
            
            // Attach styles with error handling
            try {
                attachStyles();
            } catch (styleError) {
                console.warn('Style attachment failed:', styleError);
                // Continue without styles - functionality more important
            }
            
            isInitialized = true;
            console.log('MultiOrder initialized successfully');
            return true;
            
        } catch (error) {
            console.error('MultiOrder init failed:', error);
            isInitialized = false; // Ensure we can retry
            return false;
        }
    };

    // Detection - core logic, no layers
    const detect = (content) => {
        if (!content || typeof content !== 'string') {
            return { isMultiOrder: false, confidence: 0, orders: [] };
        }

        // Quick pattern check
        const matchCount = PATTERNS.reduce((count, pattern) => {
            return count + (pattern.test(content) ? 1 : 0);
        }, 0);

        if (matchCount === 0) {
            return { isMultiOrder: false, confidence: 0, orders: [] };
        }

        // Extract orders - simple line-based detection
        const lines = content.split('\n').filter(line => line.trim());
        const orders = [];
        
        lines.forEach((line, index) => {
            // Look for order indicators
            if (/订单|order|预订|booking/i.test(line)) {
                orders.push({
                    id: `order_${orders.length + 1}`,
                    content: line.trim(),
                    index: index,
                    selected: false,
                    status: 'detected'
                });
            }
        });

        const isMultiOrder = orders.length >= 2;
        const confidence = isMultiOrder ? Math.min(0.9, 0.5 + (orders.length * 0.1)) : 0.1;

        return { isMultiOrder, confidence, orders, orderCount: orders.length };
    };

    // Process - no event emission bullshit
    const process = (orders) => {
        if (!Array.isArray(orders) || orders.length === 0) {
            throw new Error('Invalid orders data');
        }

        // Simple processing - add timestamps and validate
        const processed = orders.map((order, index) => ({
            ...order,
            id: order.id || `order_${index + 1}`,
            processed: true,
            timestamp: new Date().toISOString(),
            status: 'ready'
        }));

        currentOrders = processed;
        return processed;
    };

    // UI - minimal, functional - FIXED: Added safety checks
    const showUI = (orders = []) => {
        if (!isInitialized) {
            console.warn('MultiOrder not initialized, attempting to initialize...');
            initialize().then(() => {
                if (isInitialized) {
                    showUI(orders); // Retry after initialization
                }
            }).catch(error => {
                console.error('Failed to initialize MultiOrder:', error);
                alert('Failed to initialize multi-order system');
            });
            return false;
        }

        if (!container) {
            console.error('Container not available');
            return false;
        }

        if (orders.length > 0) {
            currentOrders = orders;
        }

        // Safety check for orders
        if (!Array.isArray(currentOrders)) {
            currentOrders = [];
        }

        try {
            container.innerHTML = `
                <div class="multi-order-header">
                    <h3>Multi-Order Processing</h3>
                    <button class="close-btn" onclick="if(window.MultiOrder) window.MultiOrder.hideUI()">×</button>
                </div>
                <div class="multi-order-content">
                    <div class="status">Ready (${currentOrders.length} orders)</div>
                    <div class="orders-list">
                        ${currentOrders.map((order, index) => `
                            <div class="order-item ${order.selected ? 'selected' : ''}" 
                                 data-id="${order.id || index}" 
                                 onclick="if(window.MultiOrder) window.MultiOrder.toggleOrder('${order.id || index}')">
                                <div class="order-content">${(order.content || '').substring(0, 100)}${order.content && order.content.length > 100 ? '...' : ''}</div>
                                <div class="order-status">Status: ${order.status || 'pending'}</div>
                            </div>
                        `).join('')}
                    </div>
                    <div class="controls">
                        <button onclick="if(window.MultiOrder) window.MultiOrder.processSelected()">Process Selected</button>
                        <button onclick="if(window.MultiOrder) window.MultiOrder.selectAll()">Select All</button>
                        <button onclick="if(window.MultiOrder) window.MultiOrder.hideUI()">Cancel</button>
                    </div>
                </div>
            `;

            container.style.display = 'block';
            return true;
            
        } catch (error) {
            console.error('Failed to show UI:', error);
            updateStatus('UI Error: ' + error.message);
            return false;
        }
    };

    const hideUI = () => {
        if (container) container.style.display = 'none';
    };

    // Order management - direct manipulation
    const toggleOrder = (orderId) => {
        const order = currentOrders.find(o => o.id === orderId);
        if (order) {
            order.selected = !order.selected;
            // Update UI element
            const element = container.querySelector(`[data-id="${orderId}"]`);
            if (element) {
                element.classList.toggle('selected', order.selected);
            }
        }
    };

    const selectAll = () => {
        currentOrders.forEach(order => order.selected = true);
        container.querySelectorAll('.order-item').forEach(el => {
            el.classList.add('selected');
        });
    };

    const processSelected = async () => {
        // FIXED: Added safety checks and better error handling
        if (!isInitialized) {
            console.warn('MultiOrder not initialized');
            alert('System not ready, please try again');
            return;
        }
        
        const selected = currentOrders.filter(order => order.selected);
        if (selected.length === 0) {
            alert('Please select orders to process');
            return;
        }

        // Prevent multiple simultaneous processing
        if (container.classList.contains('processing')) {
            console.warn('Already processing orders');
            return;
        }

        try {
            // Mark as processing to prevent duplicates
            container.classList.add('processing');
            
            // Update status with safety check
            updateStatus('Processing...');
            
            // Batch processing with limits to prevent freeze
            const maxBatchSize = 10; // Limit batch size
            const batchesToProcess = selected.slice(0, maxBatchSize);
            
            if (selected.length > maxBatchSize) {
                console.warn(`Processing limited to ${maxBatchSize} orders to prevent freeze`);
                updateStatus(`Processing first ${maxBatchSize} of ${selected.length} orders...`);
            }
            
            for (let i = 0; i < batchesToProcess.length; i++) {
                try {
                    batchesToProcess[i].status = 'processing';
                    updateProgress((i + 1) / batchesToProcess.length * 100);
                    
                    // Reduced delay and added timeout protection
                    await new Promise(resolve => setTimeout(resolve, 50));
                    
                    batchesToProcess[i].status = 'completed';
                    batchesToProcess[i].processedAt = new Date().toISOString();
                    
                } catch (itemError) {
                    console.error(`Failed to process order ${i + 1}:`, itemError);
                    batchesToProcess[i].status = 'error';
                    batchesToProcess[i].error = itemError.message;
                }
            }

            // Save to history with timeout protection
            try {
                await Promise.race([
                    saveToHistory(batchesToProcess),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Save timeout')), 5000))
                ]);
            } catch (saveError) {
                console.warn('Failed to save to history:', saveError);
                // Continue without saving to history
            }
            
            updateStatus(`Completed ${batchesToProcess.length} orders`);
            
            // Auto-hide after success (optional)
            setTimeout(() => {
                if (container.classList.contains('processing')) {
                    hideUI();
                }
            }, 2000);
            
        } catch (error) {
            updateStatus(`Error: ${error.message}`);
            console.error('Processing failed:', error);
        } finally {
            // Always remove processing flag
            container.classList.remove('processing');
        }
    };

    // History - simple integration
    const saveToHistory = async (orders) => {
        if (!historyManager) return false;

        try {
            // Use new multi-order method if available
            if (typeof historyManager.addMultiOrderHistory === 'function') {
                const detection = { isMultiOrder: true, confidence: 0.9 };
                await historyManager.addMultiOrderHistory(orders, detection, {
                    timestamp: new Date().toISOString(),
                    status: 'completed'
                });
            } else {
                // Fallback to regular save
                const historyEntry = {
                    id: `multi_order_${Date.now()}`,
                    type: 'multi-order',
                    orders: orders,
                    timestamp: new Date().toISOString()
                };
                
                if (typeof historyManager.addOrder === 'function') {
                    await historyManager.addOrder(historyEntry, historyEntry.id);
                }
            }
            
            console.log('Orders saved to history:', orders.length);
            return true;
            
        } catch (error) {
            console.error('History save failed:', error);
            return false;
        }
    };

    // Utilities
    // FIXED: Safe status update with error handling
    const updateStatus = (message) => {
        try {
            const statusEl = container?.querySelector('.status');
            if (statusEl) {
                statusEl.textContent = message;
                console.log('MultiOrder status:', message);
            }
        } catch (error) {
            console.warn('Failed to update status:', error);
        }
    };

    const updateProgress = (percent) => {
        // Simple progress indication with safety
        try {
            updateStatus(`Processing... ${Math.round(percent)}%`);
        } catch (error) {
            console.warn('Failed to update progress:', error);
        }
    };

    // Minimal CSS - embedded, no external files
    const attachStyles = () => {
        const styleId = 'multi-order-styles';
        if (document.getElementById(styleId)) return;

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .multi-order-module {
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                background: white; border: 1px solid #ccc; border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 10000;
                max-width: 600px; max-height: 500px; overflow: auto; display: none;
            }
            .multi-order-header {
                padding: 16px 20px; border-bottom: 1px solid #eee; background: #f8f9fa;
                display: flex; justify-content: space-between; align-items: center;
            }
            .multi-order-header h3 { margin: 0; font-size: 18px; }
            .close-btn {
                background: none; border: none; font-size: 20px; cursor: pointer;
                width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;
            }
            .multi-order-content { padding: 20px; }
            .status { 
                padding: 8px 12px; background: #e9ecef; border-radius: 4px; 
                margin-bottom: 16px; font-size: 14px; font-weight: 500;
            }
            .order-item {
                padding: 12px; border: 1px solid #eee; border-radius: 4px;
                margin-bottom: 8px; cursor: pointer; transition: all 0.2s;
            }
            .order-item:hover { border-color: #007bff; }
            .order-item.selected { background: #e3f2fd; border-color: #2196f3; }
            .order-content { font-weight: 500; margin-bottom: 4px; }
            .order-status { font-size: 12px; color: #6c757d; }
            .controls { margin-top: 16px; text-align: right; }
            .controls button {
                padding: 8px 16px; margin-left: 8px; border: none; border-radius: 4px;
                cursor: pointer; font-size: 14px;
            }
            .controls button:first-child { background: #007bff; color: white; }
            .controls button:nth-child(2) { background: #6c757d; color: white; }
            .controls button:last-child { background: #dc3545; color: white; }
        `;
        
        document.head.appendChild(style);
    };

    // Simple history fallback
    const createSimpleHistory = () => ({
        addOrder: async (data, id) => {
            const history = JSON.parse(localStorage.getItem('multi_order_history') || '[]');
            history.unshift({ ...data, id, timestamp: new Date().toISOString() });
            localStorage.setItem('multi_order_history', JSON.stringify(history.slice(0, 100)));
        }
    });

    // State getters
    const getState = () => ({
        initialized: isInitialized,
        visible: container?.style.display !== 'none',
        orders: [...currentOrders]
    });

    const getOrders = () => [...currentOrders];
    const isVisible = () => container?.style.display !== 'none';

    // Public API - clean and minimal
    return {
        // Core functions
        initialize,
        detect,
        process,
        
        // UI functions  
        showUI,
        hideUI,
        toggleOrder,
        selectAll,
        processSelected,
        
        // Utility functions
        saveToHistory,
        getState,
        getOrders,
        isVisible
    };
})();

// Auto-initialize when DOM is ready - FIXED: Delay initialization to avoid circular dependencies
if (typeof document !== 'undefined') {
    // Use setTimeout to ensure all scripts are loaded and avoid blocking
    const delayedInit = () => {
        if (typeof window.MultiOrder !== 'undefined' && window.MultiOrder.initialize) {
            // Only initialize if not already done
            window.MultiOrder.initialize().catch(error => {
                console.warn('MultiOrder delayed initialization failed:', error);
            });
        }
    };
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(delayedInit, 100); // Small delay to avoid conflicts
        });
    } else {
        setTimeout(delayedInit, 50); // Even smaller delay if DOM already ready
    }
}

// Export for both module and global use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MultiOrder;
} else if (typeof window !== 'undefined') {
    window.MultiOrder = MultiOrder;
}

// Note: ES6 export removed to avoid syntax errors in non-module script context
// export default MultiOrder;