/**
 * ============================================================================
 * 🚀 核心业务流程 - 结果处理器 (子层实现)
 * ============================================================================
 *
 * @fileoverview 结果处理器 - 子层实现
 * @description 负责多订单模式触发判断和结果处理，本地逻辑处理
 * 
 * @businessFlow 本地结果处理和多订单模式触发判断
 * 在核心业务流程中的位置：
 * 输入内容 → 渠道检测 → 提示词组合 → Gemini API调用
 *     ↓
 * Gemini返回解析结果
 *     ↓
 * 【当前文件职责】本地结果处理 - 本地处理
 *     ↓
 * B1. 单订单 → 映射到单订单表单 → 发送GoMyHire API
 * B2. 多订单 → 触发多订单模式 → 映射到多订单表单 → 批量发送API
 *     ↓
 * 保存到本地历史订单并持久化
 *
 * @architecture Child Layer (子层) - 本地处理实现
 * - 职责：结果处理的具体实现
 * - 原则：专注单一功能，不依赖其他子层
 * - 接口：为母层提供结果处理服务
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - controllers/business-flow-controller.js (母层控制器调用)
 * 下游依赖：无（底层实现）
 *
 * @localProcessing 本地处理职责（核心功能）
 * - 🟢 多订单模式触发判断
 * - 🟢 单订单/多订单结果分类
 * - 🟢 订单数据格式化和验证
 * - 🟢 结果置信度计算
 * - 🟢 错误处理和数据修复
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯本地处理模块）
 *
 * @compatibility 兼容性保证
 * - 保持现有结果格式不变
 * - 兼容现有的多订单检测逻辑
 * - 保持向后兼容的数据结构
 *
 * @refactoringConstraints 重构约束
 * - ✅ 不能调用远程API（严格本地处理）
 * - ✅ 不能依赖其他子层
 * - ✅ 必须保持结果处理的准确性
 * - ✅ 保持现有的判断逻辑
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 结果处理器 - 子层实现
     */
    class ResultProcessor {
        constructor() {
            this.logger = this.getLogger();
            
            // 多订单判断配置
            this.multiOrderConfig = {
                minOrderCount: 2,
                minConfidence: 0.7,
                maxSingleOrderLength: 500 // 单个订单文本的最大长度
            };
            
            this.logger.log('结果处理器已初始化', 'info');
        }

        /**
         * 处理结果 - 主要入口
         * @param {object} geminiResult - Gemini解析结果
         * @param {object} channelResult - 渠道检测结果
         * @param {object} options - 处理选项
         * @returns {Promise<object>} 处理结果
         */
        async processResult(geminiResult, channelResult, options = {}) {
            try {
                this.logger.log('开始处理结果', 'info', { 
                    hasGeminiResult: !!geminiResult,
                    channel: channelResult.channel 
                });

                // 验证输入数据
                if (!geminiResult) {
                    throw new Error('Gemini解析结果为空');
                }

                // 判断是否为多订单
                const isMultiOrder = this.isMultiOrder(geminiResult);
                
                let processedResult;
                if (isMultiOrder) {
                    processedResult = await this.handleMultiOrder(geminiResult, channelResult, options);
                } else {
                    processedResult = await this.handleSingleOrder(geminiResult, channelResult, options);
                }

                // 添加处理元数据
                processedResult.metadata = {
                    processedAt: new Date().toISOString(),
                    channel: channelResult.channel,
                    confidence: channelResult.confidence,
                    processingTime: Date.now()
                };

                this.logger.log('结果处理完成', 'success', { 
                    type: processedResult.type,
                    orderCount: processedResult.type === 'multi-order' ? processedResult.orders.length : 1
                });

                return processedResult;

            } catch (error) {
                this.logger.log('结果处理失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 判断是否为多订单
         * @param {object} result - Gemini解析结果
         * @returns {boolean} 是否为多订单
         */
        isMultiOrder(result) {
            try {
                // 仅依赖Gemini输出，不再进行本地启发式判断
                if (Array.isArray(result?.orders) && result.orders.length >= this.multiOrderConfig.minOrderCount) {
                    return true;
                }
                if (result?.type === 'multi-order' || result?.isMultiOrder === true) {
                    return true;
                }
                return false;
            } catch (error) {
                this.logger.log('多订单判断失败', 'error', { error: error.message });
                return false;
            }
        }

        /**
         * 处理单订单
         * @param {object} geminiResult - Gemini解析结果
         * @param {object} channelResult - 渠道检测结果
         * @param {object} options - 选项
         * @returns {Promise<object>} 处理结果
         */
        async handleSingleOrder(geminiResult, channelResult, options = {}) {
            try {
                // 格式化单订单数据
                const order = this.formatOrderData(geminiResult, channelResult);
                
                // 数据验证已移除，直接使用AI解析结果

                return {
                    type: 'single-order',
                    order: order,
                    orders: [order], // 为了兼容性，也提供orders数组
                    channel: channelResult.channel,
                    confidence: geminiResult.confidence || 0.8
                };

            } catch (error) {
                this.logger.log('单订单处理失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 处理多订单
         * @param {object} geminiResult - Gemini解析结果
         * @param {object} channelResult - 渠道检测结果
         * @param {object} options - 选项
         * @returns {Promise<object>} 处理结果
         */
        async handleMultiOrder(geminiResult, channelResult, options = {}) {
            try {
                // 仅当Gemini提供了orders数组时才按多订单处理，否则降级为单订单
                if (!Array.isArray(geminiResult.orders) || geminiResult.orders.length === 0) {
                    this.logger.log('未提供orders数组，降级为单订单处理', 'warning');
                    return await this.handleSingleOrder(geminiResult, channelResult, options);
                }

                const orders = geminiResult.orders;

                // 格式化每个订单
                const formattedOrders = orders.map((order, index) => {
                    return this.formatOrderData(order, channelResult, { orderIndex: index });
                });

                // 直接使用所有格式化订单
                const validOrders = formattedOrders;

                if (validOrders.length < this.multiOrderConfig.minOrderCount) {
                    this.logger.log('有效订单数量不足，转为单订单处理', 'warning', { 
                        validCount: validOrders.length 
                    });
                    return await this.handleSingleOrder(geminiResult, channelResult, options);
                }

                // 触发多订单模式 - 调用独立的多订单模组
                this.triggerMultiOrderMode(validOrders, geminiResult);

                return {
                    type: 'multi-order',
                    order: null, // 多订单模式下没有单个订单
                    orders: validOrders,
                    channel: channelResult.channel,
                    confidence: geminiResult.confidence || 0.8,
                    orderCount: validOrders.length
                };

            } catch (error) {
                this.logger.log('多订单处理失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 触发多订单模式 - 直接调用，无废话
         * @param {Array} orders - 订单数组
         * @param {object} geminiResult - Gemini原始结果
         */
        async triggerMultiOrderMode(orders, geminiResult) {
            try {
                // Use the globally loaded MultiOrder module
                if (!window.MultiOrder) {
                    throw new Error('MultiOrder module not available');
                }
                
                // Process and show - that's it
                const processed = window.MultiOrder.process(orders);
                window.MultiOrder.showUI(processed);
                
                // Save to history automatically
                await window.MultiOrder.saveToHistory(processed);
                
                this.logger.log('Multi-order mode triggered', 'info', { orderCount: orders.length });

            } catch (error) {
                this.logger.log('Multi-order trigger failed', 'error', { error: error.message });
                // Don't break the flow
            }
        }

        /**
         * 格式化订单数据
         * @param {object} orderData - 原始订单数据
         * @param {object} channelResult - 渠道检测结果
         * @param {object} options - 格式化选项
         * @returns {object} 格式化后的订单数据
         */
        formatOrderData(orderData, channelResult, options = {}) {
            try {
                // 允许值为 0/false 的字段按原值保留，因此不能用 || 短路，改用显式判断
                const pick = (obj, key, def = null) => {
                    return Object.prototype.hasOwnProperty.call(obj, key) ? obj[key] : def;
                };

                // 处理价格计算过程（如果存在）
                if (orderData.price_calculation) {
                    this.logger.log('检测到价格计算过程', 'info', {
                        calculation: orderData.price_calculation,
                        channel: channelResult.channel
                    });

                    // 验证价格计算是否正确
                    if (channelResult.channel === 'fliggy') {
                        this.validateFliggyPriceCalculation(orderData);
                    }
                }

                // 先构建严格字段白名单（与 PromptBuilder 字段定义一致）
                const skeleton = {
                    customer_name: null,
                    customer_contact: null,
                    customer_email: null,
                    ota: null,
                    ota_reference_number: null,
                    flight_info: null,
                    departure_time: null,
                    arrival_time: null,
                    flight_type: null,
                    pickup_date: null,
                    pickup_time: null,
                    pickup_location: null,
                    dropoff_location: null,
                    passenger_count: null,
                    luggage_count: null,
                    sub_category_id: null,
                    car_type_id: null,
                    driving_region_id: null,
                    baby_chair: null,
                    tour_guide: null,
                    meet_and_greet: null,
                    needs_paging_service: null,
                    ota_price: null,
                    currency: null,
                    extra_requirement: null,
                    // 添加价格计算过程字段
                    price_calculation: null
                };

                // 组装输出，仅从白名单字段映射，避免额外字段泄漏
                const mapped = {
                    customer_name: pick(orderData, 'customer_name', null),
                    customer_contact: pick(orderData, 'customer_contact', null),
                    customer_email: pick(orderData, 'customer_email', null),
                    ota: pick(orderData, 'ota', null) || (channelResult.channel || null),
                    ota_reference_number: pick(orderData, 'ota_reference_number', null),
                    flight_info: pick(orderData, 'flight_info', null),
                    departure_time: pick(orderData, 'departure_time', null),
                    arrival_time: pick(orderData, 'arrival_time', null),
                    flight_type: pick(orderData, 'flight_type', null),
                    date: pick(orderData, 'date', null),
                    time: pick(orderData, 'time', null),
                    pickup: pick(orderData, 'pickup', null),
                    destination: pick(orderData, 'destination', null),
                    passenger_number: pick(orderData, 'passenger_number', null),
                    luggage_number: pick(orderData, 'luggage_number', null),
                    sub_category_id: pick(orderData, 'sub_category_id', null),
                    car_type_id: pick(orderData, 'car_type_id', null),
                    driving_region_id: pick(orderData, 'driving_region_id', null),
                    baby_chair: pick(orderData, 'baby_chair', null),
                    tour_guide: pick(orderData, 'tour_guide', null),
                    meet_and_greet: pick(orderData, 'meet_and_greet', null),
                    needs_paging_service: pick(orderData, 'needs_paging_service', null),
                    ota_price: pick(orderData, 'ota_price', null),
                    currency: pick(orderData, 'currency', null),
                    extra_requirement: pick(orderData, 'extra_requirement', null),
                    // 包含价格计算过程
                    price_calculation: pick(orderData, 'price_calculation', null)
                };

                // 合并 skeleton，确保所有键存在（未知保持为 null）
                const formatted = { ...skeleton, ...mapped };

                // 附加本地元数据（不暴露到后续API，如需隐藏可在适配器里剔除）
                formatted.confidence = pick(orderData, 'confidence', 0.8);
                formatted.orderIndex = options.orderIndex || 0;
                formatted.processedAt = new Date().toISOString();

                return formatted;

            } catch (error) {
                this.logger.log('订单数据格式化失败', 'error', { error: error.message });
                return orderData; // 返回原始数据作为降级方案
            }
        }


        /**
         * 检查是否为有效订单（简化版）
         * @param {object} order - 订单数据
         * @returns {boolean} 是否有效
         */
        isValidOrder(order) {
            return order && typeof order === 'object';
        }

        /**
         * 从结果中提取订单
         * @param {object} result - Gemini结果
         * @returns {array} 订单数组
         */
        extractOrdersFromResult(result) {
            // 本地提取逻辑已废弃：多订单由Gemini决定
            return Array.isArray(result?.orders) ? result.orders : [];
        }

        /**
         * 验证飞猪价格计算过程
         * @param {object} orderData - 订单数据
         */
        validateFliggyPriceCalculation(orderData) {
            try {
                const calc = orderData.price_calculation;
                if (!calc) return;

                const { original_price, channel_rate, region_rate, final_price } = calc;

                // 验证计算是否正确
                const expectedPrice = Math.round(original_price * channel_rate * region_rate * 100) / 100;
                const actualPrice = parseFloat(final_price);

                if (Math.abs(expectedPrice - actualPrice) > 0.01) {
                    this.logger.log('🔴 价格计算验证失败', 'warning', {
                        expected: expectedPrice,
                        actual: actualPrice,
                        calculation: calc,
                        formula: `${original_price} × ${channel_rate} × ${region_rate} = ${expectedPrice}`
                    });

                    // 🔧 修复：即使计算验证失败，也应该使用final_price而不是错误的ota_price
                    if (orderData.ota_price !== actualPrice && actualPrice > 0) {
                        this.logger.log('🔧 使用final_price修正ota_price字段（即使验证失败）', 'info', {
                            oldPrice: orderData.ota_price,
                            newPrice: actualPrice
                        });
                        orderData.ota_price = actualPrice;
                    }

                    // 标记计算错误
                    orderData._price_calculation_error = true;
                    orderData._expected_price = expectedPrice;
                } else {
                    this.logger.log('✅ 价格计算验证通过', 'success', {
                        price: actualPrice,
                        calculation: calc,
                        formula: `${original_price} × ${channel_rate} × ${region_rate} = ${expectedPrice}`
                    });

                    // 🔧 修复：确保ota_price与final_price保持一致
                    if (orderData.ota_price !== actualPrice) {
                        this.logger.log('🔧 修正ota_price字段以匹配final_price', 'info', {
                            oldPrice: orderData.ota_price,
                            newPrice: actualPrice
                        });
                        orderData.ota_price = actualPrice;
                    }

                    orderData._price_calculation_verified = true;
                }

            } catch (error) {
                this.logger.log('价格计算验证出错', 'error', { error: error.message });
            }
        }

        /**
         * 获取处理统计信息
         * @returns {object} 统计信息
         */
        getProcessingStats() {
            return {
                multiOrderConfig: this.multiOrderConfig,
                version: '2.0.0',
                supportedTypes: ['single-order', 'multi-order']
            };
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const resultProcessor = new ResultProcessor();

    // 导出到全局作用域
    window.ResultProcessor = ResultProcessor;
    window.OTA.ResultProcessor = ResultProcessor;
    window.OTA.resultProcessor = resultProcessor;

    console.log('✅ ResultProcessor (子层实现) 已加载');

})();
